import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { NodeRendererProps } from '../NodeFactory';
import { compareNodeWithProperties } from '@/lib/memo-utils';

const YourNewNodeComponent = forwardRef<HTMLDivElement, NodeRendererProps & React.HTMLAttributes<HTMLDivElement>>(({ 
  node,
  ...htmlProps
}, ref) => {
  // Extract properties from node
  const someProperty = node.properties?.someProperty as string;
  
  return (
    <div 
      ref={ref}
      className={cn(
        "your-default-classes",
        node.style?.className
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      {/* Your node content here */}
      <div>{someProperty}</div>
    </div>
  );
});

YourNewNodeComponent.displayName = 'YourNewNode';

// Memoize the component for performance
export const YourNewNode = React.memo(YourNewNodeComponent, compareNodeWithProperties);