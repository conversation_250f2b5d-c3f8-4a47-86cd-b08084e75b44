// @/features/demo/states/api.ts

import { API_URL, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";

// Storage API endpoints
const STORAGE_UPLOAD_URL = API_ENDPOINTS.STORAGE.PUBLIC.UPLOAD;
const STORAGE_VIEW_URL = API_ENDPOINTS.STORAGE.PUBLIC.VIEW;

// Storage file response interface
export interface StorageFile {
  id: string | null;
  name: string;
  parent: string | null;
  createBy: string | null;
  createdAt: number;
  updatedAt: number;
  size: number;
  version: string | null;
  contentType: string | null;
  extension: string | null;
  deleted: boolean;
  tokenConfirm: string | null;
  dataStream: string | null;
}

// Upload response interface
export interface UploadResponse {
  code: string;
  message: string;
  data: StorageFile;
}

/**
 * Upload file to storage
 * @param file - File to upload
 * @returns Promise with upload response containing file info
 */
export async function uploadFile(file: File): Promise<UploadResponse> {
  const formData = new FormData();
  formData.append("file", file);

  const res = await restApi.post<UploadResponse>(
    `${API_URL}${STORAGE_UPLOAD_URL}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );

  return res.data;
}

/**
 * Get full URL for viewing/accessing uploaded file
 * @param fileName - File name from upload response (data.name)
 * @returns Full URL to access the file
 */
export function getFileViewUrl(fileName: string): string {
  return `${API_URL}${STORAGE_VIEW_URL}/${fileName}`;
}
