import React from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { TreeNode } from "../../../context/types";

interface ContentFormProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

/**
 * ContentForm handles node-specific content properties
 * Used in the "Nội dung" (Content) tab of the Properties Panel
 */
export const ContentForm: React.FC<ContentFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const renderContentByType = () => {
    switch (node.type) {
      case "text":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="text-content">Nội dung văn bản</Label>
              <Textarea
                id="text-content"
                value={(node.properties?.content as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      content: e.target.value,
                    },
                  })
                }
                placeholder="Nhập nội dung văn bản..."
                rows={4}
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "image":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="image-src" className="block mb-2">
                Đường dẫn hình ảnh
              </Label>
              <Textarea
                id="image-src"
                value={(node.properties?.src as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      src: e.target.value,
                    },
                  })
                }
                placeholder="https://example.com/image.jpg"
                rows={2}
                disabled={disabled}
              />
            </div>
            <div>
              <Label htmlFor="image-alt" className="block mb-2">
                Mô tả hình ảnh
              </Label>
              <Textarea
                id="image-alt"
                value={(node.properties?.alt as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      alt: e.target.value,
                    },
                  })
                }
                placeholder="Mô tả chi tiết về hình ảnh"
                rows={3}
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "video":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="video-src">URL video</Label>
              <Input
                id="video-src"
                value={(node.properties?.src as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      src: e.target.value,
                    },
                  })
                }
                placeholder="https://example.com/video.mp4"
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "imageWithCaption":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="caption-image-src">URL hình ảnh</Label>
              <Input
                id="caption-image-src"
                value={(node.properties?.src as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      src: e.target.value,
                    },
                  })
                }
                placeholder="https://example.com/image.jpg"
                disabled={disabled}
              />
            </div>
            <div>
              <Label htmlFor="caption-image-alt">Mô tả ảnh (Alt text)</Label>
              <Input
                id="caption-image-alt"
                value={(node.properties?.alt as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      alt: e.target.value,
                    },
                  })
                }
                placeholder="Mô tả ngắn gọn về hình ảnh"
                disabled={disabled}
              />
            </div>
            <div>
              <Label htmlFor="caption-text">Chú thích</Label>
              <Textarea
                id="caption-text"
                value={(node.properties?.caption as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      caption: e.target.value,
                    },
                  })
                }
                placeholder="Nhập chú thích cho hình ảnh..."
                rows={3}
                disabled={disabled}
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="text-sm text-gray-500">
            Nội dung cho node type "{node.type}" đang được phát triển...
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
          <p className="text-sm text-yellow-800">
            Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.
          </p>
        </div>
      )}
      {renderContentByType()}
    </div>
  );
};
