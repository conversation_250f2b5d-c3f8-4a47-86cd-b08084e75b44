import React from 'react';
import { TreeNode } from '../../context/types';
import { FrameNode, TextNode, ImageNode, VideoNode } from './basic';
import { DoublePanelsCardNode, ImageWithCaptionNode } from './templates';
import { DateTimeCardNode, YourNewNode } from './widgets';

export interface NodeRendererProps {
  node: TreeNode;
}

export type NodeComponent = React.FC<NodeRendererProps>;

const nodeRegistry: Record<string, NodeComponent> = {
  // Basic nodes
  frame: FrameNode,
  text: TextNode,
  image: ImageNode,
  video: VideoNode,

  // Template nodes
  imageWithCaption: ImageWithCaptionNode,
  doublePanelsCard: DoublePanelsCardNode,

  // Widget nodes
  dateTimeCard: DateTimeCardNode,
  yourNewNodeType: YourNewNode, // Add your new node here
};


export function renderNode(props: NodeRendererProps): React.ReactElement | null {
  const NodeComponent = nodeRegistry[props.node.type];

  if (!NodeComponent) {
    // Fallback to frame for unknown node types
    return <FrameNode {...props} />;
  }

  return <NodeComponent {...props} />;
}