// PRODUCTION-SAFE LOGGER UTILITY
const isDevelopment = process.env.NODE_ENV === "development";

export const logger = {
  error: (message: string, ...args: any[]) => {
    if (isDevelopment || process.env.VITE_DEBUG === "true") {
      console.error(`[Builder Error] ${message}`, ...args);
    }
    // In production, send to monitoring service
    // trackError(message, args);
  },

  warn: (message: string, ...args: any[]) => {
    if (isDevelopment) {
      console.warn(`[Builder Warning] ${message}`, ...args);
    }
  },

  debug: (message: string, ...args: any[]) => {
    if (isDevelopment && process.env.VITE_DEBUG === "true") {
      console.log(`[Builder Debug] ${message}`, ...args);
    }
  },

  info: (message: string, ...args: any[]) => {
    if (isDevelopment) {
      console.info(`[Builder Info] ${message}`, ...args);
    }
  },
};

// Performance tracking utility
export const perfTracker = {
  start: (label: string) => {
    if (isDevelopment) {
      performance.mark(`${label}-start`);
    }
  },

  end: (label: string) => {
    if (isDevelopment) {
      performance.mark(`${label}-end`);
      performance.measure(label, `${label}-start`, `${label}-end`);
      const measure = performance.getEntriesByName(label)[0];
      logger.debug(
        `Performance: ${label} took ${measure.duration.toFixed(2)}ms`
      );
    }
  },
};
