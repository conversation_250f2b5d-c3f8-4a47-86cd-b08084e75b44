import {
  Post,
  <PERSON><PERSON>erUIState,
  PersistentUIState,
  TemporaryUIState,
  StorageSchema,
  SerializedUIState,
  SerializedTemporaryUIState,
  StorageError,
  BuilderState,
  HistoryState,
} from "./types";
import { STORAGE_CONFIG } from "../config/constants";
import { toast } from "sonner";

/**
 * Storage management with versioning and error handling
 */

// Type guards
const isStorageSchema = (data: unknown): data is StorageSchema => {
  if (!data || typeof data !== "object") return false;

  const schema = data as Record<string, unknown>;
  return (
    typeof schema.version === "number" &&
    typeof schema.data === "object" &&
    typeof schema.metadata === "object"
  );
};

// Migration functions
type MigrationFunction = (oldData: unknown) => StorageSchema | null;

const migrations: Record<number, MigrationFunction> = {
  // Version 1 to 2: Convert old {type, value} format to direct values
  2: (oldData: unknown): StorageSchema | null => {
    try {
      const migrateNode = (
        node: Record<string, unknown>
      ): Record<string, unknown> => {
        if (!node || typeof node !== "object") return node;

        // Migrate properties from {type, value} to direct values
        if (node.properties && typeof node.properties === "object") {
          const oldProperties = node.properties as Record<string, unknown>;
          const newProperties: Record<string, unknown> = {};

          for (const [key, prop] of Object.entries(oldProperties)) {
            if (prop && typeof prop === "object" && "value" in prop) {
              // Old format detected, extract value
              const oldProp = prop as { value: unknown };
              newProperties[key] = oldProp.value;
            } else {
              // Already in new format or null
              newProperties[key] = prop;
            }
          }
          node.properties = newProperties;
        }

        // Recursively migrate children
        if (node.children && Array.isArray(node.children)) {
          node.children = (node.children as Record<string, unknown>[]).map(
            migrateNode
          );
        }

        return node;
      };

      if (!oldData || typeof oldData !== "object") return null;
      const data = oldData as Record<string, unknown>;

      // Migrate the post content
      if (data.data && typeof data.data === "object") {
        const dataObj = data.data as Record<string, unknown>;
        if (dataObj.post && typeof dataObj.post === "object") {
          const postObj = dataObj.post as Record<string, unknown>;
          if (postObj.content && typeof postObj.content === "object") {
            postObj.content = migrateNode(
              postObj.content as Record<string, unknown>
            );
          }
        }
      }

      // Update version
      data.version = 2;

      return data as unknown as StorageSchema;
    } catch (error) {
      console.error("Migration v1->v2 failed:", error);
      return null;
    }
  },
};

// Apply migrations
const applyMigrations = (data: StorageSchema): StorageSchema | null => {
  let currentData = data;

  // Apply each migration from current version to latest
  for (let v = data.version + 1; v <= STORAGE_CONFIG.CURRENT_VERSION; v++) {
    const migration = migrations[v];
    if (migration) {
      const migrated = migration(currentData);
      if (!migrated) return null;
      currentData = migrated;
    }
  }

  return currentData;
};

// Serialize persistent UI state only
const serializePersistentUIState = (
  uiState: BuilderUIState
): SerializedUIState => ({
  selectedNodeId: uiState.selectedNodeId,
  expandedNodeIds: Array.from(uiState.expandedNodeIds),
  viewMode: uiState.viewMode,
  showTreePanel: uiState.showTreePanel,
  showPropertiesPanel: uiState.showPropertiesPanel,
});

// Serialize temporary UI state
const serializeTemporaryUIState = (
  uiState: BuilderUIState
): SerializedTemporaryUIState => ({
  propertiesPanelMainTab: uiState.temporary?.propertiesPanelMainTab,
  propertiesPanelSubTab: uiState.temporary?.propertiesPanelSubTab,
  propertiesPanelAdvanced: uiState.temporary?.propertiesPanelAdvanced,
  propertiesPanelDisplaySection:
    uiState.temporary?.propertiesPanelDisplaySection,
  // Legacy support
  propertiesPanelTab: uiState.temporary?.propertiesPanelTab,
  isAdvancedMode: uiState.temporary?.isAdvancedMode,
});

// Deserialize persistent UI state
const deserializePersistentUIState = (
  serialized: SerializedUIState
): Partial<PersistentUIState> => ({
  selectedNodeId: serialized.selectedNodeId,
  expandedNodeIds: new Set(serialized.expandedNodeIds),
  viewMode: serialized.viewMode,
  showTreePanel: serialized.showTreePanel,
  showPropertiesPanel: serialized.showPropertiesPanel,
});

// Deserialize temporary UI state
const deserializeTemporaryUIState = (
  serialized: SerializedTemporaryUIState
): TemporaryUIState => ({
  propertiesPanelMainTab: serialized.propertiesPanelMainTab,
  propertiesPanelSubTab: serialized.propertiesPanelSubTab,
  propertiesPanelAdvanced: serialized.propertiesPanelAdvanced,
  propertiesPanelDisplaySection: serialized.propertiesPanelDisplaySection,
  // Legacy support
  propertiesPanelTab: serialized.propertiesPanelTab,
  isAdvancedMode: serialized.isAdvancedMode,
});

// Storage class
export class BuilderStorage {
  // Save post
  static savePost(post: Post): void {
    try {
      console.log("Saving post to storage:", post.id);
      localStorage.setItem(STORAGE_CONFIG.KEYS.POST, JSON.stringify(post));
      toast.success("Post saved successfully!");
    } catch (error) {
      console.error("Failed to save post:", error);
      throw new Error("Failed to save post to storage");
    }
  }

  // Save persistent UI state
  static savePersistentUIState(uiState: BuilderUIState): void {
    try {
      const serialized = serializePersistentUIState(uiState);
      localStorage.setItem(
        STORAGE_CONFIG.KEYS.UI_STATE,
        JSON.stringify(serialized)
      );
    } catch (error) {
      console.error("Failed to save persistent UI state:", error);
    }
  }

  // Save temporary UI state
  static saveTemporaryUIState(uiState: BuilderUIState): void {
    try {
      const serialized = serializeTemporaryUIState(uiState);
      localStorage.setItem(
        STORAGE_CONFIG.KEYS.TEMP_UI_STATE,
        JSON.stringify(serialized)
      );
    } catch (error) {
      console.error("Failed to save temporary UI state:", error);
    }
  }

  // Legacy save UI state (for backward compatibility)
  static saveUIState(uiState: BuilderUIState): void {
    this.savePersistentUIState(uiState);
    this.saveTemporaryUIState(uiState);
  }

  // Save history
  static saveHistory(history: Partial<HistoryState>): void {
    try {
      localStorage.setItem(
        STORAGE_CONFIG.KEYS.HISTORY,
        JSON.stringify(history)
      );
    } catch (error) {
      console.error("Failed to save history:", error);
    }
  }

  // Save complete state
  static saveCompleteState(
    state: BuilderState,
    history?: Partial<HistoryState>
  ): void {
    try {
      const schema: StorageSchema = {
        version: STORAGE_CONFIG.CURRENT_VERSION,
        data: {
          post: state.post!,
          uiState: serializePersistentUIState(state.uiState),
          temporaryUIState: serializeTemporaryUIState(state.uiState),
          history: history
            ? {
                baseState: history.baseState!,
                actions: history.actions || [],
                currentIndex: history.currentIndex || -1,
              }
            : undefined,
        },
        metadata: {
          lastSaved: Date.now(),
          createdAt: state.post?.createdAt || Date.now(),
        },
      };

      localStorage.setItem(STORAGE_CONFIG.KEYS.POST, JSON.stringify(schema));
      localStorage.setItem(
        STORAGE_CONFIG.KEYS.LAST_SAVE,
        Date.now().toString()
      );

      // Also save temporary state separately for immediate access
      this.saveTemporaryUIState(state.uiState);
    } catch (error) {
      console.error("Failed to save complete state:", error);
      throw new Error("Failed to save state to storage");
    }
  }

  // Load complete state
  static loadCompleteState(): {
    state: Partial<BuilderState>;
    history?: Partial<HistoryState>;
    error?: StorageError;
  } {
    try {
      const raw = localStorage.getItem(STORAGE_CONFIG.KEYS.POST);
      if (!raw) {
        return { state: {} };
      }

      const parsed = JSON.parse(raw);

      // Check if it's the new schema format
      if (isStorageSchema(parsed)) {
        // Check version
        if (parsed.version > STORAGE_CONFIG.CURRENT_VERSION) {
          return {
            state: {},
            error: {
              type: "VERSION_MISMATCH",
              message: `Storage version ${parsed.version} is newer than current version ${STORAGE_CONFIG.CURRENT_VERSION}`,
              data: parsed,
            },
          };
        }

        // Apply migrations if needed
        let data = parsed;
        if (parsed.version < STORAGE_CONFIG.CURRENT_VERSION) {
          const migrated = applyMigrations(parsed);
          if (!migrated) {
            return {
              state: {},
              error: {
                type: "VERSION_MISMATCH",
                message: "Failed to migrate storage data",
                data: parsed,
              },
            };
          }
          data = migrated;
        }

        // Extract state
        const persistentUI = deserializePersistentUIState(data.data.uiState);

        // Try to get temporary UI state from schema first, then fallback to separate key
        let temporaryUI: TemporaryUIState = {};
        if (data.data.temporaryUIState) {
          temporaryUI = deserializeTemporaryUIState(data.data.temporaryUIState);
        } else {
          // Fallback: try to load from separate key
          try {
            const tempRaw = localStorage.getItem(
              STORAGE_CONFIG.KEYS.TEMP_UI_STATE
            );
            if (tempRaw) {
              const tempParsed = JSON.parse(tempRaw);
              temporaryUI = deserializeTemporaryUIState(tempParsed);
            }
          } catch (error) {
            console.warn(
              "Failed to load temporary UI state from fallback:",
              error
            );
          }
        }

        const state: Partial<BuilderState> = {
          post: data.data.post,
          uiState: {
            // Persistent state
            ...persistentUI,
            // Session state defaults
            editorMode: "edit" as const,
            isDirty: false,
            lastSaved: 0,
            copiedNode: null,
            lastCreatedNodeId: null,
            // Temporary state
            temporary: temporaryUI,
          } as BuilderUIState,
        };

        return {
          state,
          history: data.data.history,
        };
      } else {
        // Legacy format - treat as post only
        return {
          state: {
            post: parsed as Post,
          },
        };
      }
    } catch (error) {
      console.error("Failed to load state:", error);
      return {
        state: {},
        error: {
          type: "PARSE_ERROR",
          message: "Failed to parse storage data",
          data: error,
        },
      };
    }
  }

  // Get last save time
  static getLastSaveTime(): number {
    try {
      const time = localStorage.getItem(STORAGE_CONFIG.KEYS.LAST_SAVE);
      return time ? parseInt(time, 10) : 0;
    } catch {
      return 0;
    }
  }

  // Check if has unsaved changes
  static hasUnsavedChanges(): boolean {
    const lastSave = this.getLastSaveTime();
    return Date.now() - lastSave < 10000; // Less than 10 seconds ago
  }

  // Clear all storage
  static clearAll(): void {
    Object.values(STORAGE_CONFIG.KEYS).forEach((key) => {
      localStorage.removeItem(key);
    });
  }

  // Export all data
  static exportAll(): string {
    const data = {
      version: STORAGE_CONFIG.CURRENT_VERSION,
      post: localStorage.getItem(STORAGE_CONFIG.KEYS.POST),
      uiState: localStorage.getItem(STORAGE_CONFIG.KEYS.UI_STATE),
      history: localStorage.getItem(STORAGE_CONFIG.KEYS.HISTORY),
      exported: new Date().toISOString(),
    };

    return JSON.stringify(data, null, 2);
  }

  // Import data
  static importData(jsonString: string): boolean {
    try {
      const data = JSON.parse(jsonString);

      if (data.post) {
        localStorage.setItem(STORAGE_CONFIG.KEYS.POST, data.post);
      }
      if (data.uiState) {
        localStorage.setItem(STORAGE_CONFIG.KEYS.UI_STATE, data.uiState);
      }
      if (data.history) {
        localStorage.setItem(STORAGE_CONFIG.KEYS.HISTORY, data.history);
      }

      return true;
    } catch (error) {
      console.error("Failed to import data:", error);
      return false;
    }
  }
}
