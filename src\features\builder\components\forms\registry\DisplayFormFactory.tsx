import React, { useState } from "react";
import { TreeNode, TreeNodeType } from "../../../context/types";
import {
  SizeSection,
  LayoutSection,
  SpacingSection,
  ImageSection,
  OverflowSection,
  PositionSection,
  BorderSection,
  AppearanceSection,
  RoundedSection,
  ShadowSection,
  FilterSection,
  TypographySection,
  TransformSection,
  InteractionSection,
  AdvancedBackgroundSection,
} from "../sections";
import { CollapsibleSection } from "../ui";
import { Button } from "@/components/ui/button";
import { useUI } from "../../../context/UIContext";

export interface DisplayFormProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

export type DisplayFormComponent = React.FC<DisplayFormProps>;

export interface DisplayFormRegistry {
  [key: string]: DisplayFormComponent;
}

// Frame Display Form with collapsible sections
const FrameDisplayForm: DisplayFormComponent = ({
  node,
  onChange,
  disabled,
}) => {
  const { uiState, setPropertiesPanelDisplaySection } = useUI();
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Get current section from context, default to 'size'
  const openSection =
    uiState?.temporary?.propertiesPanelDisplaySection ?? "size";

  const handleSectionToggle = (sectionId: string) => {
    // If clicking the same section, close it; otherwise open the new one
    const newSection = openSection === sectionId ? "" : sectionId;
    setPropertiesPanelDisplaySection(newSection);
  };

  return (
    <div className="space-y-3">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
          <p className="text-sm text-yellow-800">
            Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.
          </p>
        </div>
      )}

      {/* Size Section */}
      <CollapsibleSection
        title="Kích thước"
        isOpen={openSection === "size"}
        onToggle={() => handleSectionToggle("size")}
        disabled={disabled}
      >
        <SizeSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Layout Section */}
      <CollapsibleSection
        title="Bố cục"
        isOpen={openSection === "layout"}
        onToggle={() => handleSectionToggle("layout")}
        disabled={disabled}
      >
        <LayoutSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Overflow Section */}
      <CollapsibleSection
        title="Tràn nội dung"
        isOpen={openSection === "overflow"}
        onToggle={() => handleSectionToggle("overflow")}
        disabled={disabled}
      >
        <OverflowSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Spacing Controls */}
      <CollapsibleSection
        title="Khoảng cách"
        isOpen={openSection === "spacing"}
        onToggle={() => handleSectionToggle("spacing")}
        disabled={disabled}
      >
        <SpacingSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Typography Controls - Moved up */}
      <CollapsibleSection
        title="Chữ viết"
        isOpen={openSection === "typography"}
        onToggle={() => handleSectionToggle("typography")}
        disabled={disabled}
      >
        <TypographySection
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      </CollapsibleSection>

      {/* Basic Sections - Always Visible */}

      {/* Appearance Controls */}
      <CollapsibleSection
        title="Giao diện"
        isOpen={openSection === "appearance"}
        onToggle={() => handleSectionToggle("appearance")}
        disabled={disabled}
      >
        <AppearanceSection
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      </CollapsibleSection>

      {/* Border & Corner Controls */}
      <CollapsibleSection
        title="Viền"
        isOpen={openSection === "border"}
        onToggle={() => handleSectionToggle("border")}
        disabled={disabled}
      >
        <BorderSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      <CollapsibleSection
        title="Góc bo tròn"
        isOpen={openSection === "rounded"}
        onToggle={() => handleSectionToggle("rounded")}
        disabled={disabled}
      >
        <RoundedSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Effects Controls */}
      <CollapsibleSection
        title="Đổ bóng"
        isOpen={openSection === "shadow"}
        onToggle={() => handleSectionToggle("shadow")}
        disabled={disabled}
      >
        <ShadowSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Show More/Less Button */}
      <div className="pt-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          disabled={disabled}
          className="w-full text-xs h-8"
        >
          {showAdvanced ? "− Ẩn bớt" : "+ Xem thêm"}
        </Button>
      </div>

      {/* Advanced Sections - Show when toggled */}
      {showAdvanced && (
        <>
          {/* Position Section */}
          <CollapsibleSection
            title="Vị trí"
            isOpen={openSection === "position"}
            onToggle={() => handleSectionToggle("position")}
            disabled={disabled}
          >
            <PositionSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          {/* Display & Transform Controls */}
          <CollapsibleSection
            title="Biến đổi"
            isOpen={openSection === "transform"}
            onToggle={() => handleSectionToggle("transform")}
            disabled={disabled}
          >
            <TransformSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Bộ lọc"
            isOpen={openSection === "filter"}
            onToggle={() => handleSectionToggle("filter")}
            disabled={disabled}
          >
            <FilterSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          {/* Advanced Background Controls */}
          <CollapsibleSection
            title="Nền nâng cao"
            isOpen={openSection === "advancedBackground"}
            onToggle={() => handleSectionToggle("advancedBackground")}
            disabled={disabled}
          >
            <AdvancedBackgroundSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          {/* Interaction Controls */}
          <CollapsibleSection
            title="Tương tác"
            isOpen={openSection === "interaction"}
            onToggle={() => handleSectionToggle("interaction")}
            disabled={disabled}
          >
            <InteractionSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>
        </>
      )}

      {/* Spacer để dễ scroll xuống cuối */}
      <div className="h-20" />
    </div>
  );
};

// Text Display Form - Typography focused
const TextDisplayForm: DisplayFormComponent = ({
  node,
  onChange,
  disabled,
}) => {
  const { uiState, setPropertiesPanelDisplaySection } = useUI();
  const [showAdvanced, setShowAdvanced] = useState(false);

  const openSection =
    uiState?.temporary?.propertiesPanelDisplaySection ?? "typography";

  const handleSectionToggle = (sectionId: string) => {
    const newSection = openSection === sectionId ? "" : sectionId;
    setPropertiesPanelDisplaySection(newSection);
  };

  return (
    <div className="space-y-3">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
          <p className="text-sm text-yellow-800">
            Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.
          </p>
        </div>
      )}

      {/* Typography - Main feature for text */}
      <CollapsibleSection
        title="Chữ viết"
        isOpen={openSection === "typography"}
        onToggle={() => handleSectionToggle("typography")}
        disabled={disabled}
      >
        <TypographySection
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      </CollapsibleSection>

      {/* Spacing */}
      <CollapsibleSection
        title="Khoảng cách"
        isOpen={openSection === "spacing"}
        onToggle={() => handleSectionToggle("spacing")}
        disabled={disabled}
      >
        <SpacingSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Appearance */}
      <CollapsibleSection
        title="Giao diện"
        isOpen={openSection === "appearance"}
        onToggle={() => handleSectionToggle("appearance")}
        disabled={disabled}
      >
        <AppearanceSection
          node={node}
          onChange={onChange}
          disabled={disabled}
        />
      </CollapsibleSection>

      {/* Border */}
      <CollapsibleSection
        title="Viền"
        isOpen={openSection === "border"}
        onToggle={() => handleSectionToggle("border")}
        disabled={disabled}
      >
        <BorderSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Show More/Less Button */}
      <div className="pt-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          disabled={disabled}
          className="w-full text-xs h-8"
        >
          {showAdvanced ? "− Ẩn bớt" : "+ Xem thêm"}
        </Button>
      </div>

      {/* Advanced Sections */}
      {showAdvanced && (
        <>
          <CollapsibleSection
            title="Vị trí"
            isOpen={openSection === "position"}
            onToggle={() => handleSectionToggle("position")}
            disabled={disabled}
          >
            <PositionSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Biến đổi"
            isOpen={openSection === "transform"}
            onToggle={() => handleSectionToggle("transform")}
            disabled={disabled}
          >
            <TransformSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Đổ bóng"
            isOpen={openSection === "shadow"}
            onToggle={() => handleSectionToggle("shadow")}
            disabled={disabled}
          >
            <ShadowSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Tương tác"
            isOpen={openSection === "interaction"}
            onToggle={() => handleSectionToggle("interaction")}
            disabled={disabled}
          >
            <InteractionSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>
        </>
      )}

      <div className="h-20" />
    </div>
  );
};

// Image Display Form - Image focused
const ImageDisplayForm: DisplayFormComponent = ({
  node,
  onChange,
  disabled,
}) => {
  const { uiState, setPropertiesPanelDisplaySection } = useUI();
  const [showAdvanced, setShowAdvanced] = useState(false);

  const openSection =
    uiState?.temporary?.propertiesPanelDisplaySection ?? "size";

  const handleSectionToggle = (sectionId: string) => {
    const newSection = openSection === sectionId ? "" : sectionId;
    setPropertiesPanelDisplaySection(newSection);
  };

  return (
    <div className="space-y-3">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
          <p className="text-sm text-yellow-800">
            Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.
          </p>
        </div>
      )}

      {/* Size - Important for images */}
      <CollapsibleSection
        title="Kích thước"
        isOpen={openSection === "size"}
        onToggle={() => handleSectionToggle("size")}
        disabled={disabled}
      >
        <SizeSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Image - Main feature for images */}
      <CollapsibleSection
        title="Hình ảnh"
        isOpen={openSection === "image"}
        onToggle={() => handleSectionToggle("image")}
        disabled={disabled}
      >
        <ImageSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Spacing */}
      <CollapsibleSection
        title="Khoảng cách"
        isOpen={openSection === "spacing"}
        onToggle={() => handleSectionToggle("spacing")}
        disabled={disabled}
      >
        <SpacingSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Border */}
      <CollapsibleSection
        title="Viền"
        isOpen={openSection === "border"}
        onToggle={() => handleSectionToggle("border")}
        disabled={disabled}
      >
        <BorderSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Rounded */}
      <CollapsibleSection
        title="Góc bo tròn"
        isOpen={openSection === "rounded"}
        onToggle={() => handleSectionToggle("rounded")}
        disabled={disabled}
      >
        <RoundedSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Shadow */}
      <CollapsibleSection
        title="Đổ bóng"
        isOpen={openSection === "shadow"}
        onToggle={() => handleSectionToggle("shadow")}
        disabled={disabled}
      >
        <ShadowSection node={node} onChange={onChange} disabled={disabled} />
      </CollapsibleSection>

      {/* Show More/Less Button */}
      <div className="pt-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          disabled={disabled}
          className="w-full text-xs h-8"
        >
          {showAdvanced ? "− Ẩn bớt" : "+ Xem thêm"}
        </Button>
      </div>

      {/* Advanced Sections */}
      {showAdvanced && (
        <>
          <CollapsibleSection
            title="Vị trí"
            isOpen={openSection === "position"}
            onToggle={() => handleSectionToggle("position")}
            disabled={disabled}
          >
            <PositionSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Biến đổi"
            isOpen={openSection === "transform"}
            onToggle={() => handleSectionToggle("transform")}
            disabled={disabled}
          >
            <TransformSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Bộ lọc"
            isOpen={openSection === "filter"}
            onToggle={() => handleSectionToggle("filter")}
            disabled={disabled}
          >
            <FilterSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>

          <CollapsibleSection
            title="Tương tác"
            isOpen={openSection === "interaction"}
            onToggle={() => handleSectionToggle("interaction")}
            disabled={disabled}
          >
            <InteractionSection
              node={node}
              onChange={onChange}
              disabled={disabled}
            />
          </CollapsibleSection>
        </>
      )}

      <div className="h-20" />
    </div>
  );
};

// Default Display Form
const DefaultDisplayForm: DisplayFormComponent = ({ node, disabled }) => {
  return (
    <div className="space-y-4">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
          <p className="text-sm text-yellow-800">
            Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.
          </p>
        </div>
      )}
      <div className="text-sm text-gray-500">
        Display form cho node type "{node.type}" đang được phát triển...
      </div>
    </div>
  );
};

/**
 * Registry of display forms for each node type
 */
const displayFormRegistry: DisplayFormRegistry = {
  frame: FrameDisplayForm,
  text: TextDisplayForm,
  image: ImageDisplayForm,
  video: ImageDisplayForm, // Video can reuse Image form
  imageWithCaption: ImageDisplayForm, // Template can reuse Image form
  dateTimeCard: FrameDisplayForm, // Widget can reuse Frame form
  doublePanelsCard: FrameDisplayForm, // Widget can reuse Frame form
};

/**
 * Register a custom display form for a node type
 */
export const registerDisplayForm = (
  nodeType: TreeNodeType,
  form: DisplayFormComponent
) => {
  displayFormRegistry[nodeType] = form;
};

/**
 * Get display form for a node type
 */
export const getDisplayForm = (
  nodeType: TreeNodeType
): DisplayFormComponent => {
  return displayFormRegistry[nodeType] || DefaultDisplayForm;
};

/**
 * Render display form for a node
 */
export const renderDisplayForm = (props: DisplayFormProps) => {
  const DisplayForm = getDisplayForm(props.node.type);
  return <DisplayForm {...props} />;
};
