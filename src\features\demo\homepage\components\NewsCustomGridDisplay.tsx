import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { NewsArticleCard } from "./NewsArticleCard";
import { Post } from "@/features/post/states/types";
import { GridPagination } from "@/components/grid/GridPagination";
import { GridHeader } from "@/components/grid/GridHeader";

export interface NewsCustomGridDisplayProps {
  title?: string;
  posts: Post[];
  itemsPerPage?: number;
  className?: string;
  onArticleClick?: (post: Post) => void;
}

export const NewsCustomGridDisplay: React.FC<NewsCustomGridDisplayProps> = ({
  title,
  posts,
  itemsPerPage = 4,
  className,
  onArticleClick,
}) => {
  const [currentPage, setCurrentPage] = useState(0);

  const publishedPosts = posts.filter(post => post.status.includes(""));

  if (publishedPosts.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-gray-500"><PERSON><PERSON><PERSON><PERSON> c<PERSON> bài viết nào để hiển thị</p>
      </div>
    );
  }

  const totalPages = Math.ceil(publishedPosts.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPosts = publishedPosts.slice(startIndex, endIndex);

  const handlePrevious = () => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  return (
    <div className={cn("space-y-6", className)}>
      {title && (
        <GridHeader
          length={publishedPosts.length}
          itemsPerPage={itemsPerPage}
          title={title}
          onPrevious={handlePrevious}
          onNext={handleNext}
        />
      )}

      <div className="flex flex-row gap-8">
        {/* Left Side - Featured Article (Large) */}
        {currentPosts[0] && (
          <div className="flex-1">
            <NewsArticleCard
              post={currentPosts[0]}
              onClick={() => onArticleClick?.(currentPosts[0])}
              darkMode
              displayTag
            />
          </div>
        )}

        {/* Right Side - Grid of Smaller Articles */}
        <div className="flex-1 flex flex-col gap-8">
          {currentPosts[1] && (
            <NewsArticleCard
              post={currentPosts[1]}
              onClick={() => onArticleClick?.(currentPosts[1])}
              darkMode
              displayTag
            />
          )}
          {currentPosts.slice(2, 4).length > 0 && (
            <div className="flex flex-row gap-8">
              {currentPosts.slice(2, 4).map((post) => (
                <NewsArticleCard
                  post={post}
                  onClick={() => onArticleClick?.(post)}
                  darkMode
                  displayTag
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {totalPages > 1 && (
        <GridPagination
          length={publishedPosts.length}
          itemsPerPage={itemsPerPage}
          onChangePagination={(index) => setCurrentPage(index)}
        />
      )}
    </div>
  );
};
